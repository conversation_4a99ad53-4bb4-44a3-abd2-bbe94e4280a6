<template>
    <div>
        <v-app-bar elevation="2" color="primary">
            <v-container class="d-flex justify-space-between align-center">
                <!-- 用户信息 -->
                <div class="d-flex align-center">
                    <v-avatar size="32" class="mr-2" color="white">
                        <v-icon color="primary">mdi-account</v-icon>
                    </v-avatar>
                    <span class="text-white font-weight-medium">{{ username }}</span>
                </div>

                <!-- 导航标签 -->
                <v-tabs v-model="activeTab" @update:model-value="handleTabChange" centered class="flex-grow-1">
                <v-tab value="1">订单 Orders</v-tab>
                <v-tab value="2">商家</v-tab>
                <v-tab value="3">结算</v-tab>
                <v-tab value="4">列表</v-tab>
                <v-tab value="5">权限</v-tab>
                <v-menu>
                    <template v-slot:activator="{ props }">
                        <v-tab v-bind="props" value="6">市场</v-tab>
                    </template>
                    <v-list>
                        <v-list-item 
                            value="6-1" 
                            @click="handleMenuItemClick('6-1')">
                            <v-list-item-title>司机管理</v-list-item-title>
                        </v-list-item>
                        <v-list-item 
                            value="6-2" 
                            @click="handleMenuItemClick('6-2')">
                            <v-list-item-title>申请补偿审核</v-list-item-title>
                        </v-list-item>
                    </v-list>
                </v-menu>
                <v-tab value="7">高级</v-tab>
            </v-tabs>

                <!-- 退出登录按钮 -->
                <v-btn
                    variant="outlined"
                    color="white"
                    size="small"
                    @click="handleLogout"
                    class="ml-4"
                >
                    <v-icon left class="mr-1">mdi-logout</v-icon>
                    退出
                </v-btn>
            </v-container>
        </v-app-bar>
        <v-main>
            <RouterView />
        </v-main>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeTab = ref('1')
const username = ref('')

// 获取用户名
onMounted(() => {
    const storedUsername = localStorage.getItem('username')
    username.value = storedUsername || '未知用户'
})

const handleTabChange = (value: unknown) => {
    console.log('Tab changed to:', value)
}

const handleMenuItemClick = (key: string) => {
    console.log('Menu item clicked:', key)
}

// 退出登录
const handleLogout = () => {
    // 清除登录状态
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('username')

    // 跳转到登录页面
    router.push('/login')
}
</script>

<style scoped>
.v-app-bar .v-container {
    max-width: 100%;
}

.v-tabs {
    flex: none;
}
</style>