<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>

    <v-container fluid class="fill-height">
      <v-row justify="center" align="center" class="fill-height">
        <v-col cols="12" sm="8" md="6" lg="4" xl="3">
          <v-card class="login-card" elevation="12" rounded="xl">
            <!-- 登录头部 -->
            <v-card-text class="text-center pa-8">
              <div class="login-header mb-6">
                <v-avatar size="80" class="mb-4" color="primary">
                  <v-icon size="40" color="white">mdi-account-circle</v-icon>
                </v-avatar>
                <h1 class="text-h4 font-weight-bold text-primary mb-2">欢迎登录</h1>
                <p class="text-body-1 text-grey-darken-1">请输入您的账号信息</p>
              </div>

              <!-- 登录表单 -->
              <v-form ref="loginForm" v-model="formValid" @submit.prevent="handleLogin">
                <div class="login-form">
                  <!-- 账号输入 -->
                  <v-text-field
                    v-model="loginData.username"
                    :rules="usernameRules"
                    label="账号"
                    prepend-inner-icon="mdi-account"
                    variant="outlined"
                    class="mb-4"
                    :loading="loading"
                    :disabled="loading"
                    clearable
                    required
                  />

                  <!-- 密码输入 -->
                  <v-text-field
                    v-model="loginData.password"
                    :rules="passwordRules"
                    :type="showPassword ? 'text' : 'password'"
                    label="密码"
                    prepend-inner-icon="mdi-lock"
                    :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                    @click:append-inner="showPassword = !showPassword"
                    variant="outlined"
                    class="mb-4"
                    :loading="loading"
                    :disabled="loading"
                    required
                  />

                  <!-- 记住密码 -->
                  <div class="d-flex justify-space-between align-center mb-6">
                    <v-checkbox
                      v-model="rememberPassword"
                      label="记住密码"
                      color="primary"
                      density="compact"
                      hide-details
                      :disabled="loading"
                    />
                    <v-btn
                      variant="text"
                      color="primary"
                      size="small"
                      @click="handleForgotPassword"
                      :disabled="loading"
                    >
                      忘记密码？
                    </v-btn>
                  </div>

                  <!-- 登录按钮 -->
                  <v-btn
                    type="submit"
                    color="primary"
                    size="large"
                    block
                    class="mb-4 login-btn"
                    :loading="loading"
                    :disabled="!formValid || loading"
                    rounded="lg"
                  >
                    <v-icon left class="mr-2">mdi-login</v-icon>
                    登录
                  </v-btn>

                  <!-- 浏览器兼容性提示 -->
                  <v-alert
                    type="warning"
                    variant="tonal"
                    density="compact"
                    class="mt-4"
                    icon="mdi-alert-circle-outline"
                  >
                    <div class="text-caption">
                      请尽可能避免使用任何国产浏览器（如"QQ"、"360"等），以保证所有功能的正常使用。
                    </div>
                  </v-alert>
                </div>
              </v-form>
            </v-card-text>
          </v-card>

          <!-- 版权信息 -->
          <div class="text-center mt-4">
            <p class="text-caption text-grey">
              © 2025 订单管理系统. All rights reserved.
            </p>
          </div>
        </v-col>
      </v-row>
    </v-container>

    <!-- 消息提示 -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      :timeout="3000"
      location="top"
    >
      {{ snackbar.message }}
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          关闭
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const loginForm = ref(null)
const formValid = ref(false)
const loading = ref(false)
const showPassword = ref(false)
const rememberPassword = ref(false)

const loginData = ref({
  username: '',
  password: ''
})

// 表单验证规则
const usernameRules = [
  v => !!v || '请输入账号',
  v => (v && v.length >= 3) || '账号至少3个字符'
]

const passwordRules = [
  v => !!v || '请输入密码',
  v => (v && v.length >= 6) || '密码至少6个字符'
]

// 消息提示
const snackbar = ref({
  show: false,
  message: '',
  color: 'success'
})

// 显示消息
const showMessage = (message, color = 'success') => {
  snackbar.value = {
    show: true,
    message,
    color
  }
}

// 处理登录
const handleLogin = async () => {
  if (!formValid.value) return

  loading.value = true
  
  try {
    // 模拟登录API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 简单的登录验证（实际项目中应该调用后端API）
    if (loginData.value.username === 'admin' && loginData.value.password === '123456') {
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('username', loginData.value.username)
      
      // 如果选择记住密码，保存到localStorage
      if (rememberPassword.value) {
        localStorage.setItem('rememberedUsername', loginData.value.username)
        localStorage.setItem('rememberedPassword', loginData.value.password)
      } else {
        localStorage.removeItem('rememberedUsername')
        localStorage.removeItem('rememberedPassword')
      }
      
      showMessage('登录成功！正在跳转...', 'success')
      
      // 延迟跳转到订单报告页面
      setTimeout(() => {
        router.push('/order-report')
      }, 1000)
    } else {
      showMessage('账号或密码错误，请重试', 'error')
    }
  } catch (error) {
    showMessage('登录失败，请稍后重试', 'error')
  } finally {
    loading.value = false
  }
}

// 处理忘记密码
const handleForgotPassword = () => {
  showMessage('请联系管理员重置密码', 'info')
}



// 组件挂载时检查是否有记住的密码
onMounted(() => {
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  const rememberedPassword = localStorage.getItem('rememberedPassword')
  
  if (rememberedUsername && rememberedPassword) {
    loginData.value.username = rememberedUsername
    loginData.value.password = rememberedPassword
    rememberPassword.value = true
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.login-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
}

.login-btn {
  font-weight: bold;
  text-transform: none;
  letter-spacing: 0.5px;
}



/* 响应式设计 */
@media (max-width: 600px) {
  .login-card {
    margin: 16px;
  }

  .circle-1, .circle-2, .circle-3 {
    display: none;
  }
}
</style>
