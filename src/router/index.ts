import { createWebHistory, createRouter } from 'vue-router'

import Layout from '@/layout/index.vue'
import Home from '@/views/Home.vue'
import Login from '@/views/Login.vue'
import OrderReport from '@/views/OrderReport.vue'
import OrderReport2 from '@/views/OrderReport2.vue'

const routes = [
  {
    path: '/login',
    name: "Login",
    component: Login
  },
  {
    path: '/',
    name: "Index",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "/home",
        name: "Home",
        component: Home
      },
      {
        path: "/order-report",
        name: "OrderReport",
        component: OrderReport
      },
      {
        path: "/order-report2",
        name: "OrderReport2",
        component: OrderReport2
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = localStorage.getItem('isLoggedIn')

  // 如果访问登录页面且已经登录，重定向到首页
  if (to.path === '/login' && isLoggedIn) {
    next('/home')
    return
  }

  // 如果访问需要登录的页面但未登录，重定向到登录页面
  if (to.path !== '/login' && !isLoggedIn) {
    next('/login')
    return
  }

  next()
})

export default router