# 登录系统使用指南

## 功能特性

✨ **漂亮的登录界面**
- 使用 Vuetify 组件库打造现代化 UI
- 渐变背景和浮动动画效果
- 响应式设计，支持移动端

🔐 **完整的登录功能**
- 账号密码验证
- 记住密码功能
- 游客登录选项
- 登录状态管理

🛡️ **路由守卫**
- 未登录自动跳转到登录页
- 已登录用户无法访问登录页
- 登录后自动跳转到订单报告页面

## 测试账号

### 管理员账号
- **账号**: `admin`
- **密码**: `123456`

### 游客登录
- 点击"游客登录"按钮即可直接进入系统

## 使用步骤

1. **启动项目**
   ```bash
   cd passquan-vue3-demo
   npm run dev
   ```

2. **访问登录页面**
   - 打开浏览器访问: `http://localhost:5173/login`
   - 或者直接访问: `http://localhost:5173/` (会自动重定向到登录页)

3. **登录测试**
   - 输入测试账号密码
   - 勾选"记住密码"(可选)
   - 点击"登录"按钮

4. **登录成功后**
   - 自动跳转到订单报告页面 (`/order-report`)
   - 顶部显示用户信息和退出按钮

## 功能说明

### 记住密码功能
- 勾选"记住密码"后，下次访问会自动填充账号密码
- 数据保存在浏览器的 localStorage 中

### 路由保护
- 所有页面都需要登录才能访问
- 未登录用户会被自动重定向到登录页面
- 已登录用户访问登录页会被重定向到首页

### 退出登录
- 点击右上角的"退出"按钮
- 清除登录状态并跳转到登录页面

## 技术实现

### 前端技术栈
- **Vue 3** - 渐进式 JavaScript 框架
- **Vuetify** - Material Design 组件库
- **Vue Router** - 官方路由管理器
- **TypeScript** - 类型安全的 JavaScript

### 核心文件
- `src/views/Login.vue` - 登录页面组件
- `src/router/index.ts` - 路由配置和守卫
- `src/layout/index.vue` - 布局组件(含退出功能)

### 状态管理
- 使用 `localStorage` 存储登录状态
- 支持记住密码功能
- 简单有效的客户端状态管理

## 自定义配置

### 修改登录验证
在 `src/views/Login.vue` 的 `handleLogin` 方法中修改验证逻辑:

```javascript
// 简单的登录验证（实际项目中应该调用后端API）
if (loginData.value.username === 'admin' && loginData.value.password === '123456') {
  // 登录成功逻辑
}
```

### 修改跳转页面
在登录成功后修改跳转目标:

```javascript
// 延迟跳转到订单报告页面
setTimeout(() => {
  router.push('/order-report') // 修改这里的路径
}, 1000)
```

### 添加更多验证规则
在表单验证规则中添加更多限制:

```javascript
const usernameRules = [
  v => !!v || '请输入账号',
  v => (v && v.length >= 3) || '账号至少3个字符',
  // 添加更多规则...
]
```

## 注意事项

⚠️ **安全提醒**
- 当前使用的是前端验证，实际项目中应该使用后端API
- 密码明文存储在 localStorage 中，生产环境需要加密
- 建议集成真实的身份验证服务

🔧 **开发建议**
- 可以集成 JWT Token 认证
- 添加密码强度验证
- 实现找回密码功能
- 添加验证码功能

## 故障排除

### 常见问题

1. **页面样式异常**
   - 确保 Vuetify 正确安装和配置
   - 检查 CSS 文件是否正确导入

2. **路由跳转失败**
   - 检查路由配置是否正确
   - 确认组件文件路径无误

3. **登录状态丢失**
   - 检查 localStorage 是否被清除
   - 确认浏览器支持 localStorage

### 调试技巧
- 打开浏览器开发者工具查看控制台错误
- 检查 Network 标签页的请求状态
- 使用 Vue DevTools 调试组件状态

---

🎉 **恭喜！您已经成功创建了一个功能完整的登录系统！**

如有任何问题或需要进一步定制，请随时联系开发团队。
